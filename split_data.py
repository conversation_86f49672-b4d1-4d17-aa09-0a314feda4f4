#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据划分脚本：将结构和功能数据按被试层面1:1划分为验证集和测试集
"""

import os
import shutil
import random
import glob
from pathlib import Path
import re

def extract_subject_id(filename):
    """从文件名中提取被试ID"""
    # 匹配模式：ROISignals_XXXXX_selected.csv 或 ROI_smwpXXXXX_*.nii
    patterns = [
        r'ROISignals_(\d+)_selected\.csv',
        r'ROICorrelation_FisherZ_(\d+)_submatrix\.csv', 
        r'ROI_smwp(\d+)_.*\.nii'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            return match.group(1)
    return None

def get_subject_files(data_dir, group):
    """获取指定组的所有被试文件"""
    subject_files = {}
    
    # 功能数据 - 时间序列
    timeseries_dir = os.path.join(data_dir, 'data_fmri', 'selected_timeseries', group)
    if os.path.exists(timeseries_dir):
        for file in glob.glob(os.path.join(timeseries_dir, '*.csv')):
            subject_id = extract_subject_id(os.path.basename(file))
            if subject_id:
                if subject_id not in subject_files:
                    subject_files[subject_id] = {'fmri_timeseries': [], 'fmri_matrices': [], 'smri': []}
                subject_files[subject_id]['fmri_timeseries'].append(file)
        
        # 同时添加对应的txt文件
        for file in glob.glob(os.path.join(timeseries_dir, '*.txt')):
            subject_id = extract_subject_id(os.path.basename(file))
            if subject_id and subject_id in subject_files:
                subject_files[subject_id]['fmri_timeseries'].append(file)
    
    # 功能数据 - 相关矩阵
    matrices_dir = os.path.join(data_dir, 'data_fmri', 'subject_matrices', group)
    if os.path.exists(matrices_dir):
        for file in glob.glob(os.path.join(matrices_dir, '*.csv')):
            subject_id = extract_subject_id(os.path.basename(file))
            if subject_id and subject_id in subject_files:
                subject_files[subject_id]['fmri_matrices'].append(file)
        
        # 同时添加对应的txt文件
        for file in glob.glob(os.path.join(matrices_dir, '*.txt')):
            subject_id = extract_subject_id(os.path.basename(file))
            if subject_id and subject_id in subject_files:
                subject_files[subject_id]['fmri_matrices'].append(file)
    
    # 结构数据
    smri_dir = os.path.join(data_dir, 'data_smri', group)
    if os.path.exists(smri_dir):
        for file in glob.glob(os.path.join(smri_dir, '*.nii')):
            subject_id = extract_subject_id(os.path.basename(file))
            if subject_id and subject_id in subject_files:
                subject_files[subject_id]['smri'].append(file)
    
    return subject_files

def create_directory_structure(base_dir):
    """创建目标目录结构"""
    dirs_to_create = [
        'validation/data_fmri/selected_timeseries/HC',
        'validation/data_fmri/selected_timeseries/MCI',
        'validation/data_fmri/subject_matrices/HC',
        'validation/data_fmri/subject_matrices/MCI',
        'validation/data_smri/HC',
        'validation/data_smri/MCI',
        'test/data_fmri/selected_timeseries/HC',
        'test/data_fmri/selected_timeseries/MCI',
        'test/data_fmri/subject_matrices/HC',
        'test/data_fmri/subject_matrices/MCI',
        'test/data_smri/HC',
        'test/data_smri/MCI'
    ]
    
    for dir_path in dirs_to_create:
        full_path = os.path.join(base_dir, dir_path)
        os.makedirs(full_path, exist_ok=True)
        print(f"创建目录: {full_path}")

def copy_files(file_list, target_dir):
    """复制文件到目标目录"""
    for file_path in file_list:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            target_path = os.path.join(target_dir, filename)
            shutil.copy2(file_path, target_path)
            print(f"复制文件: {filename} -> {target_dir}")

def split_subjects(subject_files, split_ratio=0.5, random_seed=42):
    """按1:1比例划分被试"""
    random.seed(random_seed)
    subject_ids = list(subject_files.keys())
    random.shuffle(subject_ids)
    
    split_point = int(len(subject_ids) * split_ratio)
    validation_subjects = subject_ids[:split_point]
    test_subjects = subject_ids[split_point:]
    
    return validation_subjects, test_subjects

def main():
    """主函数"""
    # 设置路径
    base_dir = os.getcwd()
    print(f"工作目录: {base_dir}")
    
    # 创建目录结构
    print("\n=== 创建目录结构 ===")
    create_directory_structure(base_dir)
    
    # 处理每个组
    groups = ['HC', 'MCI']
    
    for group in groups:
        print(f"\n=== 处理 {group} 组 ===")
        
        # 获取被试文件
        subject_files = get_subject_files(base_dir, group)
        print(f"{group} 组共有 {len(subject_files)} 个被试")
        
        if len(subject_files) == 0:
            print(f"警告: {group} 组没有找到被试数据")
            continue
        
        # 划分被试
        validation_subjects, test_subjects = split_subjects(subject_files)
        print(f"验证集: {len(validation_subjects)} 个被试")
        print(f"测试集: {len(test_subjects)} 个被试")
        
        # 复制验证集文件
        print(f"\n复制 {group} 组验证集文件...")
        for subject_id in validation_subjects:
            files = subject_files[subject_id]
            
            # 复制fMRI时间序列文件
            target_dir = os.path.join(base_dir, 'validation', 'data_fmri', 'selected_timeseries', group)
            copy_files(files['fmri_timeseries'], target_dir)
            
            # 复制fMRI相关矩阵文件
            target_dir = os.path.join(base_dir, 'validation', 'data_fmri', 'subject_matrices', group)
            copy_files(files['fmri_matrices'], target_dir)
            
            # 复制sMRI文件
            target_dir = os.path.join(base_dir, 'validation', 'data_smri', group)
            copy_files(files['smri'], target_dir)
        
        # 复制测试集文件
        print(f"\n复制 {group} 组测试集文件...")
        for subject_id in test_subjects:
            files = subject_files[subject_id]
            
            # 复制fMRI时间序列文件
            target_dir = os.path.join(base_dir, 'test', 'data_fmri', 'selected_timeseries', group)
            copy_files(files['fmri_timeseries'], target_dir)
            
            # 复制fMRI相关矩阵文件
            target_dir = os.path.join(base_dir, 'test', 'data_fmri', 'subject_matrices', group)
            copy_files(files['fmri_matrices'], target_dir)
            
            # 复制sMRI文件
            target_dir = os.path.join(base_dir, 'test', 'data_smri', group)
            copy_files(files['smri'], target_dir)
        
        # 保存划分信息
        with open(f'{group}_split_info.txt', 'w', encoding='utf-8') as f:
            f.write(f"{group} 组数据划分信息\n")
            f.write(f"总被试数: {len(subject_files)}\n")
            f.write(f"验证集被试数: {len(validation_subjects)}\n")
            f.write(f"测试集被试数: {len(test_subjects)}\n\n")
            f.write("验证集被试ID:\n")
            for subject_id in validation_subjects:
                f.write(f"{subject_id}\n")
            f.write("\n测试集被试ID:\n")
            for subject_id in test_subjects:
                f.write(f"{subject_id}\n")
    
    # 复制区域信息文件
    print("\n=== 复制区域信息文件 ===")
    region_info_file = os.path.join(base_dir, 'data_fmri', 'selected_timeseries', 'selected_regions_info.csv')
    if os.path.exists(region_info_file):
        for split in ['validation', 'test']:
            target_dir = os.path.join(base_dir, split, 'data_fmri', 'selected_timeseries')
            shutil.copy2(region_info_file, target_dir)
            print(f"复制区域信息文件到: {target_dir}")
    
    print("\n=== 数据划分完成 ===")

if __name__ == "__main__":
    main()
